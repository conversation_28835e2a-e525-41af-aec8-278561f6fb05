import React, { Component } from 'react';
import { Tabs, Card, Button, message } from 'antd';
import FundingPlanTable from './FundingPlanTable';
import FundingPlanForm from './FundingPlanForm';

const { TabPane } = Tabs;

class FundingPlanManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 'list',
      selectedPlanId: null,
      selectedPlan: null,
    };
  }

  handleTabChange = (key) => {
    this.setState({ activeTab: key });
  };

  handleViewPlan = (plan) => {
    this.setState({
      selectedPlanId: plan.id,
      selectedPlan: plan,
      activeTab: 'detail',
    });
  };

  handleEditPlan = (plan) => {
    this.setState({
      selectedPlanId: plan ? plan.id : null,
      selectedPlan: plan,
      activeTab: 'form',
    });
  };

  handleCreatePlan = () => {
    this.setState({
      selectedPlanId: null,
      selectedPlan: null,
      activeTab: 'form',
    });
  };

  handleFormSubmit = (data) => {
    console.log('Form submitted:', data);
    message.success('操作成功');

    // 刷新列表
    if (this.tableRef) {
      this.tableRef.loadFundingPlans();
    }

    // 返回列表页
    this.setState({
      activeTab: 'list',
      selectedPlanId: null,
      selectedPlan: null,
    });
  };

  handleFormCancel = () => {
    this.setState({
      activeTab: 'list',
      selectedPlanId: null,
      selectedPlan: null,
    });
  };

  render() {
    const { activeTab, selectedPlanId, selectedPlan } = this.state;
    const { projectId, projectInfo } = this.props;

    return (
      <div>
        <Card
          title="资金计划管理"
          extra={
            activeTab === 'list' && (
              <Button type="primary" onClick={this.handleCreatePlan}>
                新建资金计划
              </Button>
            )
          }
        >
          <Tabs activeKey={activeTab} onChange={this.handleTabChange}>
            <TabPane tab="计划列表" key="list">
              <FundingPlanTable
                ref={(ref) => { this.tableRef = ref; }}
                projectId={projectId}
                onView={this.handleViewPlan}
                onEdit={this.handleEditPlan}
              />
            </TabPane>

            <TabPane tab="新建计划" key="form">
              <FundingPlanForm
                projectId={projectId}
                projectInfo={projectInfo}
                fundingPlan={selectedPlan}
                onSubmit={this.handleFormSubmit}
                onCancel={this.handleFormCancel}
              />
            </TabPane>

            {selectedPlanId && (
              <TabPane
                tab={`计划详情${selectedPlan ? ` - ${selectedPlan.title}` : ''}`}
                key="detail"
              >
                <Card title="资金计划详细信息">
                  {selectedPlan && (
                    <div>
                      <p><strong>计划标题：</strong>{selectedPlan.title}</p>
                      <p><strong>时间：</strong>
                        {selectedPlan.year}年{selectedPlan.month}月第{selectedPlan.weekOfMonth}周
                      </p>
                      <p><strong>资金金额：</strong>
                        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                          ¥{selectedPlan.amount?.toLocaleString()}
                        </span>
                      </p>
                      <p><strong>付款状态：</strong>
                        <span style={{
                          color: selectedPlan.isPaid ? '#52c41a' : '#faad14',
                          fontWeight: 'bold',
                        }}
                        >
                          {selectedPlan.isPaid ? '已付款' : '未付款'}
                        </span>
                      </p>
                      <p><strong>备注：</strong>{selectedPlan.remarks || '-'}</p>
                      <p><strong>创建时间：</strong>
                        {selectedPlan.createdAt
                          ? new Date(selectedPlan.createdAt).toLocaleString()
                          : '-'
                        }
                      </p>
                      <p><strong>更新时间：</strong>
                        {selectedPlan.updatedAt
                          ? new Date(selectedPlan.updatedAt).toLocaleString()
                          : '-'
                        }
                      </p>

                      <div style={{ marginTop: 24 }}>
                        <Button
                          type="primary"
                          onClick={() => this.handleEditPlan(selectedPlan)}
                          style={{ marginRight: 8 }}
                        >
                          编辑计划
                        </Button>
                        <Button onClick={() => this.setState({ activeTab: 'list' })}>
                          返回列表
                        </Button>
                      </div>
                    </div>
                  )}
                </Card>
              </TabPane>
            )}
          </Tabs>
        </Card>
      </div>
    );
  }
}

export default FundingPlanManagement;
