import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Tag,
  Popconfirm,
  message,
  Modal,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  Icon,
  Alert,
} from 'antd';
import moment from 'moment';
import {
  formatCurrency,
  formatPercentage,
  // DOCUMENT_TYPES,
  CONTRACT_TYPES,
  CONTRACT_SIGNING_STATUS,
  getContractSigningStatusConfig,
  PROJECT_STATUS,
} from '../../utils/projectUtils';
import { projectAPI, dataTransform, brandAPI } from '../../services/api';
// import { getUserNameById } from '../../mock/projectData';
import ProjectForm from './ProjectForm';
import ProjectEditModal from './ProjectEditModal';
import ProjectRevenue from './ProjectRevenue';
import WeeklyBudgetManagement from './WeeklyBudgetManagement';
import ProjectDetailModal from './ProjectDetailModal';
import ProjectChangeLog from './ProjectChangeLog';
import ErrorBoundary from '../Common/ErrorBoundary';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { PAGE_PERMISSIONS } from '../../config/permissions';


const { Option } = Select;
const { MonthPicker } = DatePicker;

const ProjectTable = () => {
  // 权限控制
  const {
    canRead,
    // canCreate,
    // canUpdate,
    canDelete,
    // canApprove,
    // isSuperAdmin,
  } = useFeaturePermission('project');

  // 财务和预算权限
  // const financePermission = useFeaturePermission('finance');
  // const budgetPermission = useFeaturePermission('budget');

  // 状态管理
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedProjectForDetail, setSelectedProjectForDetail] = useState(null);
  const [revenueModalVisible, setRevenueModalVisible] = useState(false);
  const [selectedProjectForRevenue, setSelectedProjectForRevenue] = useState(null);
  const [weeklyBudgetModalVisible, setWeeklyBudgetModalVisible] = useState(false);
  const [selectedProjectForWeeklyBudget, setSelectedProjectForWeeklyBudget] = useState(null);
  const [changeLogModalVisible, setChangeLogModalVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(moment());
  const [selectedProjectForChangeLog, setSelectedProjectForChangeLog] = useState(null);
  const [searchFilters, setSearchFilters] = useState({
    projectName: undefined,
    brand: undefined,
    contractType: undefined,
    status: undefined,
    executionPeriod: [],
  });
  const [brandOptions, setBrandOptions] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 加载品牌选项
  const loadBrandOptions = async () => {
    try {
      const response = await brandAPI.getBrands({ status: 'active' });
      if (response.success) {
        const options = response.data.brands.map((brand) => ({
          value: brand.id,
          label: brand.name,
        }));
        setBrandOptions(options);
      }
    } catch (error) {
      console.error('Load brand options failed:', error);
      setBrandOptions([]);
    }
  };

  // 加载数据
  const loadData = async (params = {}) => {
    setLoading(true);
    try {
      const requestParams = {
        page: params.page || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        keyword: searchFilters.projectName,
        brandId: searchFilters.brand,
        ...params,
      };
      if (searchFilters.status) {
        requestParams.status = searchFilters.status.toLocaleLowerCase();
      }
      if (searchFilters.contractType) {
        requestParams.contractType = searchFilters.contractType.toLocaleLowerCase();
      }
      if (searchFilters.contractSigningStatus) {
        requestParams.contractSigningStatus = searchFilters.contractSigningStatus.toLocaleLowerCase();
      }

      const response = await projectAPI.getProjects(requestParams);
      console.log('API response:', response);

      if (response.success) {
        const projects = response.data.projects.map((project) =>
          dataTransform.projectFromAPI(project));
        setDataSource(projects);
        setPagination((prev) => ({
          ...prev,
          current: response.data.page,
          total: response.data.total,
          pageSize: response.data.pageSize,
        }));
      } else {
        message.error(response.message || '获取项目列表失败');
      }
    } catch (error) {
      console.error('Load projects failed:', error);
      message.error('获取项目列表失败，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 使用指定过滤条件加载数据
  const loadDataWithFilters = async (filters, params = {}) => {
    setLoading(true);
    try {
      const requestParams = {
        page: params.page || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        keyword: filters.projectName,
        brandId: filters.brand,
        ...params,
      };
      if (filters.status) {
        requestParams.status = filters.status.toLocaleLowerCase();
      }
      if (filters.contractType) {
        requestParams.contractType = filters.contractType.toLocaleLowerCase();
      }
      if (filters.contractSigningStatus) {
        requestParams.contractSigningStatus = filters.contractSigningStatus.toLocaleLowerCase();
      }

      const response = await projectAPI.getProjects(requestParams);
      console.log('API response:', response);

      if (response.success) {
        const projects = response.data.projects.map((project) =>
          dataTransform.projectFromAPI(project));
        setDataSource(projects);
        setPagination((prev) => ({
          ...prev,
          current: response.data.page,
          total: response.data.total,
          pageSize: response.data.pageSize,
        }));
      } else {
        message.error(response.message || '获取项目列表失败');
      }
    } catch (error) {
      console.error('Load projects failed:', error);
      message.error('获取项目列表失败，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (canRead) {
      // 直接调用函数，避免依赖问题
      (async () => {
        await loadData();
        await loadBrandOptions();
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canRead]); // 只依赖权限状态

  // 事件处理方法
  const handleAdd = () => {
    setCreateModalVisible(true);
  };

  const handleEdit = (record) => {
    console.log('[ record ] >', record);
    setEditModalVisible(true);
    setEditingProject(record);
  };

  const handleViewDetail = (record) => {
    setDetailModalVisible(true);
    setSelectedProjectForDetail(record);
  };

  const handleDelete = async (record) => {
    try {
      const res = await projectAPI.deleteProject(record.id);
      if (res.success) {
        message.success('删除成功');
        loadData();
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error(`删除失败:${ error.message}`);
    }
  };

  const handleBatchDelete = () => {
    const newDataSource = dataSource.filter((item) => !selectedRowKeys.includes(item.id));
    setDataSource(newDataSource);
    setSelectedRowKeys([]);
    message.success(`批量删除成功，共删除${selectedRowKeys.length}条记录`);
  };

  const handleCreateModalCancel = () => {
    setCreateModalVisible(false);
  };

  const handleEditModalCancel = () => {
    setEditModalVisible(false);
    setEditingProject(null);
  };

  const handleDetailModalCancel = () => {
    setDetailModalVisible(false);
    setSelectedProjectForDetail(null);
  };

  // 移除未使用的 handleFormSubmit 函数

  const handleRevenueManagement = (record) => {
    setRevenueModalVisible(true);
    setSelectedProjectForRevenue(record);
  };

  const handleWeeklyBudgetManagement = (record) => {
    setWeeklyBudgetModalVisible(true);
    setSelectedProjectForWeeklyBudget(record);
  };

  const handleRevenueModalCancel = () => {
    setRevenueModalVisible(false);
    setSelectedProjectForRevenue(null);
  };

  const handleWeeklyBudgetModalCancel = () => {
    setWeeklyBudgetModalVisible(false);
    setSelectedProjectForWeeklyBudget(null);
  };

  const handleViewChangeLog = (record) => {
    setChangeLogModalVisible(true);
    setSelectedProjectForChangeLog(record);
  };

  const handleChangeLogModalCancel = () => {
    setChangeLogModalVisible(false);
    setSelectedProjectForChangeLog(null);
  };

  const handleExportWeeklyBudgets = () => {
    setExportModalVisible(true);
  };

  const handleConfirmExport = async () => {
    try {
      setExportLoading(true);
      const res = await projectAPI.exportWeeklyBudgets({
        year: selectedMonth.year(),
        month: selectedMonth.month() + 1,
      });

      if (res.success) {
        message.success('导出成功');
        // 下载文件
        console.log('[ res.data ] >', res.data);
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', res.filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setExportModalVisible(false);
      } else {
        message.error(res.message || '导出失败');
      }
    } catch (error) {
      message.error(`导出失败:${ error.message}`);
    } finally {
      setExportLoading(false);
    }
  };

  const handleCancelExport = () => {
    setExportModalVisible(false);
    setSelectedMonth(moment()); // 重置为当前月份
  };

  const handleCreateFormSubmit = () => {
    message.success('项目创建成功');
    handleCreateModalCancel();
    // 重新加载数据
    loadData();
  };

  const handleEditFormSubmit = () => {
    message.success('项目更新成功');
    handleEditModalCancel();
    // 重新加载数据
    loadData();
  };

  const handleSearch = () => {
    // 重新加载数据，使用当前的搜索条件
    loadData({ page: 1 });
  };

  // 实时搜索（防抖）
  // const handleInstantSearch = () => {
  //   if (window.searchTimer) {
  //     clearTimeout(window.searchTimer);
  //   }
  //   window.searchTimer = setTimeout(() => {
  //     handleSearch();
  //   }, 500); // 500ms防抖
  // };

  // 带参数的实时搜索（防抖）
  const handleInstantSearchWithFilters = (filters) => {
    if (window.searchTimer) {
      clearTimeout(window.searchTimer);
    }
    window.searchTimer = setTimeout(() => {
      loadDataWithFilters(filters, { page: 1 });
    }, 500); // 500ms防抖
  };

  const handleResetSearch = () => {
    const resetFilters = {
      projectName: undefined,
      brand: undefined,
      contractType: undefined,
      contractSigningStatus: undefined,
      status: undefined,
      executionPeriod: [],
    };
    setSearchFilters(resetFilters);
    // 重置后重新加载数据
    setTimeout(() => {
      loadDataWithFilters(resetFilters, { page: 1 });
    }, 0);
  };

  const updateSearchFilter = (key, value) => {
    const newFilters = {
      ...searchFilters,
      [key]: value,
    };
    setSearchFilters(newFilters);

    // 对于下拉选择器，立即搜索
    if (key !== 'projectName') {
      setTimeout(() => loadDataWithFilters(newFilters, { page: 1 }), 0);
    } else {
      // 对于文本输入，使用防抖搜索
      handleInstantSearchWithFilters(newFilters);
    }
  };

  // 移除未使用的工具函数
  // const getStatusColor, getStatusText, getBrandNameById 已移除

  // 表格列定义（包含权限控制）
  const columns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200,
      fixed: 'left',
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brand',
      width: 120,
      render: (brandName) => brandName,
    },
    {
      title: '执行周期',
      dataIndex: 'executionPeriod',
      key: 'executionPeriod',
      width: 200,
      render: (period) => {
        if (Array.isArray(period) && period.length === 2) {
          return `${period[0]} ~ ${period[1]}`;
        }
        return '-';
      },
    },
    {
      title: '规划预算',
      dataIndex: 'planningBudget',
      key: 'planningBudget',
      width: 120,
      render: (amount) => formatCurrency(amount),
    },
    {
      title: '项目利润',
      dataIndex: 'projectProfit',
      key: 'projectProfit',
      width: 120,
      render: (profit) => (
        <span style={{ color: profit >= 0 ? '#52c41a' : '#f5222d' }}>
          {formatCurrency(profit)}
        </span>
      ),
    },
    {
      title: '毛利率',
      dataIndex: 'grossMargin',
      key: 'grossMargin',
      width: 100,
      render: (margin) => (
        <span style={{ color: margin >= 0 ? '#52c41a' : '#f5222d' }}>
          {formatPercentage(margin)}
        </span>
      ),
    },
    {
      title: '执行PM',
      dataIndex: 'executivePM',
      key: 'executivePM',
      width: 120,
      render: (executivePM) => {
        if (!executivePM) return '-';
        if (typeof executivePM === 'string') return executivePM;
        if (typeof executivePM === 'object' && executivePM.name) return executivePM.name;
        return '-';
      },
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      key: 'contractType',
      width: 100,
      render: (type) => {
        const contractType = CONTRACT_TYPES.find((item) => item.value === type);
        return contractType ? contractType.label : type;
      },
    },
    {
      title: '合同签署状态',
      dataIndex: 'contractSigningStatus',
      key: 'contractSigningStatus',
      width: 120,
      render: (status) => {
        if (!status) return '-';
        const config = getContractSigningStatusConfig(status);
        return (
          <Tag color={config.color}>
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      fixed: 'right',
      render: (_, record) => (
        <>
          <FeatureGuard permissions={['project.read']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleViewDetail(record)}
              style={{ padding: '0 4px' }}
            >
              <Icon type="eye" />
              详情
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={['project.update']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleEdit(record)}
              style={{ padding: '0 4px' }}
            >
              <Icon type="edit" />
              编辑
            </Button>
          </ButtonGuard>

          <FeatureGuard permissions={['finance.read']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleRevenueManagement(record)}
              style={{ color: '#52c41a', padding: '0 4px' }}
            >
              <Icon type="dollar" />
              收入
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={['budget.read']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleWeeklyBudgetManagement(record)}
              style={{ color: '#1890ff', padding: '0 4px' }}
            >
              <Icon type="calendar" />
              预算
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={['project.read']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleViewChangeLog(record)}
              style={{ color: '#722ed1', padding: '0 4px' }}
            >
              <Icon type="history" />
              变更
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={['project.delete']}>
            <Popconfirm
              title="确定要删除这个项目吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                style={{ color: '#f5222d', padding: '0 4px' }}
              >
                <Icon type="delete" />
                删除
              </Button>
            </Popconfirm>
          </ButtonGuard>
        </>
      ),
    },
  ];


  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => {
      setSelectedRowKeys(keys);
    },
    getCheckboxProps: () => ({
      disabled: !canDelete, // 没有删除权限时禁用选择
    }),
  };

  // 权限检查 - 如果没有读取权限，显示无权限提示
  if (!canRead) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="无权限访问"
          description="您没有权限查看项目管理页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <PageGuard permissions={PAGE_PERMISSIONS.PROJECT_MANAGEMENT}>
      <div style={{ padding: '20px' }}>
        {/* 权限状态提示 */}

        <Card
          title="项目管理"
          extra={
            <>
              <ButtonGuard permissions={PAGE_PERMISSIONS.PROJECT_CREATE}>
                <Button
                  type="primary"
                  onClick={handleExportWeeklyBudgets}
                  icon="download"
                  style={{ marginRight: 8 }}
                >
                  导出周预算
                </Button>
              </ButtonGuard>
              <ButtonGuard permissions={PAGE_PERMISSIONS.PROJECT_CREATE}>
                <Button
                  type="primary"
                  onClick={handleAdd}
                  icon="plus"
                >
                  新建项目
                </Button>
              </ButtonGuard>
            </>
          }
        >
          {/* 搜索筛选区域 */}
          <div style={{
            marginBottom: 16,
            padding: 16,
            background: '#fafafa',
            borderRadius: 6,
            border: '1px solid #e8e8e8',
          }}
          >
            <Row gutter={16} align="middle">
              <Col span={5}>
                <Input
                  placeholder="搜索项目名称（支持实时搜索）"
                  value={searchFilters.projectName}
                  onChange={(e) => updateSearchFilter('projectName', e.target.value)}
                  allowClear
                  onPressEnter={handleSearch}
                />
              </Col>
              <Col span={3}>
                <Select
                  placeholder="选择品牌"
                  value={searchFilters.brand}
                  onChange={(value) => updateSearchFilter('brand', value)}
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                >
                  {brandOptions.map((brand) => (
                    <Option key={brand.value} value={brand.value}>
                      {brand.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={3}>
                <Select
                  placeholder="合同类型"
                  value={searchFilters.contractType}
                  onChange={(value) => updateSearchFilter('contractType', value)}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {CONTRACT_TYPES.map((type) => (
                    <Option key={type.value} value={type.value}>{type.label}</Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  placeholder="合同签署状态"
                  value={searchFilters.contractSigningStatus}
                  onChange={(value) => updateSearchFilter('contractSigningStatus', value)}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {CONTRACT_SIGNING_STATUS.map((status) => (
                    <Option key={status.value} value={status.value}>{status.label}</Option>
                  ))}
                </Select>
              </Col>
              <Col span={3}>
                <Select
                  placeholder="项目状态"
                  value={searchFilters.status}
                  onChange={(value) => updateSearchFilter('status', value)}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {PROJECT_STATUS.map((status) => (
                    <Option key={status.value} value={status.value}>{status.label}</Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <div>
                  <Button
                    type="primary"
                    onClick={handleSearch}
                    style={{ marginRight: 8 }}
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button onClick={handleResetSearch} style={{ marginRight: 8 }}>
                    重置
                  </Button>
                  <Button onClick={() => loadData()}>
                    刷新
                  </Button>
                </div>
              </Col>
            </Row>
          </div>

          {/* 批量操作区域 */}
          <FeatureGuard permissions={['project.delete']}>
            {selectedRowKeys.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Popconfirm
                  title={`确定要删除选中的${selectedRowKeys.length}个项目吗？`}
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="danger" style={{ marginRight: 8 }}>
                    <Icon type="delete" />
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                </Popconfirm>
              </div>
            )}
          </FeatureGuard>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            loading={loading}
            rowSelection={canDelete ? rowSelection : null}
            scroll={{ x: 1620 }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                loadData({ page, pageSize });
              },
            }}
          />
        </Card>

        {/* 项目详情弹窗 */}
        <Modal
          title={`项目详情${selectedProjectForDetail ? ` - ${selectedProjectForDetail.projectName}` : ''}`}
          visible={detailModalVisible}
          onCancel={handleDetailModalCancel}
          footer={null}
          width={1200}
          destroyOnClose
        >
          <ErrorBoundary
            title="项目详情加载失败"
            subTitle="无法显示项目详情，请稍后重试"
            onClose={handleDetailModalCancel}
          >
            {selectedProjectForDetail && (
              <ProjectDetailModal project={selectedProjectForDetail} />
            )}
          </ErrorBoundary>
        </Modal>

        {/* 新建项目弹窗 */}
        <FeatureGuard permissions={['project.create']}>
          <Modal
            title="新建项目"
            visible={createModalVisible}
            onCancel={handleCreateModalCancel}
            footer={null}
            width={1200}
            destroyOnClose
          >
            <ErrorBoundary
              title="项目表单加载失败"
              subTitle="无法显示项目表单，请稍后重试"
              onClose={handleCreateModalCancel}
            >
              <ProjectForm
                onSubmit={handleCreateFormSubmit}
                hideActions={false}
              />
            </ErrorBoundary>
          </Modal>
        </FeatureGuard>

        {/* 编辑项目弹窗 */}
        <FeatureGuard permissions={['project.update']}>
          <ProjectEditModal
            visible={editModalVisible}
            project={editingProject}
            onCancel={handleEditModalCancel}
            onSubmit={handleEditFormSubmit}
          />
        </FeatureGuard>

        {/* 收入管理弹窗 */}
        <FeatureGuard permissions={['finance.read']}>
          <Modal
            title={`项目收入管理${selectedProjectForRevenue ? ` - ${selectedProjectForRevenue.projectName}` : ''}`}
            visible={revenueModalVisible}
            onCancel={handleRevenueModalCancel}
            footer={null}
            width={1200}
            destroyOnClose
          >
            <ErrorBoundary
              title="收入管理加载失败"
              subTitle="无法显示收入管理页面，请稍后重试"
              onClose={handleRevenueModalCancel}
            >
              {selectedProjectForRevenue && (
                <ProjectRevenue
                  projectName={selectedProjectForRevenue.projectName}
                  projectId={selectedProjectForRevenue.id}
                  projectInfo={selectedProjectForRevenue}
                />
              )}
            </ErrorBoundary>
          </Modal>
        </FeatureGuard>

        {/* 周预算管理弹窗 */}
        <FeatureGuard permissions={['budget.read']}>
          <Modal
            title={`项目周预算管理${selectedProjectForWeeklyBudget ? ` - ${selectedProjectForWeeklyBudget.projectName}` : ''}`}
            visible={weeklyBudgetModalVisible}
            onCancel={handleWeeklyBudgetModalCancel}
            footer={null}
            width={1200}
            destroyOnClose
          >
            <ErrorBoundary
              title="周预算管理加载失败"
              subTitle="无法显示周预算管理页面，请稍后重试"
              onClose={handleWeeklyBudgetModalCancel}
            >
              {selectedProjectForWeeklyBudget && (
                <WeeklyBudgetManagement
                  projectId={selectedProjectForWeeklyBudget.id}
                  projectInfo={selectedProjectForWeeklyBudget}
                />
              )}
            </ErrorBoundary>
          </Modal>
        </FeatureGuard>

        {/* 项目变更记录弹窗 */}
        <FeatureGuard permissions={['project.read']}>
          <ProjectChangeLog
            visible={changeLogModalVisible}
            projectId={selectedProjectForChangeLog?.id}
            project={selectedProjectForChangeLog}
            onCancel={handleChangeLogModalCancel}
          />
        </FeatureGuard>

        {/* 月份选择导出弹窗 */}
        <Modal
          title="选择导出月份"
          visible={exportModalVisible}
          onOk={handleConfirmExport}
          onCancel={handleCancelExport}
          okText="确认导出"
          cancelText="取消"
          confirmLoading={exportLoading}
          width={400}
        >
          <div style={{ padding: '20px 0' }}>
            <p style={{ marginBottom: 16, color: '#666' }}>请选择要导出周预算数据的月份：</p>
            <MonthPicker
              value={selectedMonth}
              onChange={setSelectedMonth}
              placeholder="选择月份"
              style={{ width: '100%' }}
              format="YYYY年MM月"
            />
            <div style={{ marginTop: 16, fontSize: '12px', color: '#999' }}>
              将导出 {selectedMonth.format('YYYY年MM月')} 的所有周预算数据
            </div>
          </div>
        </Modal>
      </div>
    </PageGuard>
  );
};

export default ProjectTable;
