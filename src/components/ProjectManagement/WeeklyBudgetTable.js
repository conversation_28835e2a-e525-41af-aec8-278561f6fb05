import React, { Component } from 'react';
import {
  Table,
  Button,
  Select,
  Row,
  Col,
  Card,
  message,
  Modal,
  Tag,
  Divider,
  Popconfirm,
  // Progress,
} from 'antd';
import { weeklyBudgetAPI, approvalAPI } from '../../services/api';
import ApprovalForm from './ApprovalForm';
import ApprovalStatus from './ApprovalStatus';
import {
  SERVICE_TYPES,
  WEEKLY_BUDGET_STATUS,
  getServiceTypeLabel,
  getWeeklyBudgetStatusConfig,
  formatCurrency,
  formatDate,
  formatDateRange,
  // calculateRemainingAmount,
  getMonthWeekInfoByAddition,
  // calculatePaymentProgress,
} from '../../utils/weeklyBudgetUtils';

const { Option } = Select;
const { confirm } = Modal;

class WeeklyBudgetTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      weeklyBudgets: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      filters: {
        serviceType: '',
        status: '',
        year: '',
      },
      sorter: {
        field: 'createdAt',
        order: 'asc',
      },
      selectedRowKeys: [],
      // 审批相关状态
      showApprovalForm: false,
      showApprovalStatus: false,
      selectedBudget: null,
      currentApprovalId: null,
      approvals: {}, // 存储每个预算的审批信息
    };
  }

  componentDidMount() {
    this.loadWeeklyBudgets();
  }

  loadWeeklyBudgets = async () => {
    this.setState({ loading: true });
    try {
      const { pagination, filters, sorter } = this.state;
      const { projectId } = this.props;

      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        projectId,
        serviceType: filters.serviceType || undefined,
        status: filters.status || undefined,
        year: filters.year || undefined,
        sortBy: sorter.field,
        sortOrder: sorter.order === 'descend' ? 'desc' : 'asc',
      };

      const response = await weeklyBudgetAPI.getWeeklyBudgets(params);
      if (response.success) {
        this.setState({
          weeklyBudgets: response.data.budgets || [],
          pagination: {
            ...pagination,
            total: response.data.total,
          },
        });
      } else {
        message.error(response.message || '获取周预算列表失败');
      }
    } catch (error) {
      console.error('Load weekly budgets failed:', error);
      message.error('获取周预算列表失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  handleTableChange = (pagination, filters, sorter) => {
    this.setState(
      (prevState) => ({
        pagination: {
          ...prevState.pagination,
          current: pagination.current,
          pageSize: pagination.pageSize,
        },
        sorter: {
          field: sorter.field || 'weekStartDate',
          order: sorter.order || 'ascend',
        },
      }),
      () => {
        this.loadWeeklyBudgets();
      },
    );
  };

  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
        pagination: {
          ...prevState.pagination,
          current: 1,
        },
      }),
      () => {
        this.loadWeeklyBudgets();
      },
    );
  };

  handleDelete = async (id) => {
    try {
      const response = await weeklyBudgetAPI.deleteWeeklyBudget(id);
      if (response.success) {
        message.success('删除成功');
        this.loadWeeklyBudgets();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete weekly budget failed:', error);
      message.error('删除失败');
    }
  };

  handleBatchDelete = () => {
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的周预算');
      return;
    }

    confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个周预算吗？`,
      onOk: async () => {
        try {
          const response = await weeklyBudgetAPI.batchDeleteWeeklyBudgets(selectedRowKeys);
          if (response.success) {
            message.success('批量删除成功');
            this.setState({ selectedRowKeys: [] });
            this.loadWeeklyBudgets();
          } else {
            message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          console.error('Batch delete failed:', error);
          message.error('批量删除失败');
        }
      },
    });
  };

  handleRowSelectionChange = (selectedRowKeys) => {
    this.setState({ selectedRowKeys });
  };

  handleApprove = (record) => {
    // 检查是否有剩余金额可以申请
    // const remainingAmount = calculateRemainingAmount(record.contractAmount, record.paidAmount);
    // if (remainingAmount <= 0) {
    //   message.warning('该周预算已全部支付，无需发起审批');
    //   return;
    // }

    this.setState({
      showApprovalForm: true,
      selectedBudget: record,
    });
  };

  // 处理审批表单提交
  handleApprovalSubmit = (approvalData) => {
    this.setState((prevState) => ({
      showApprovalForm: false,
      selectedBudget: null,
      approvals: {
        ...prevState.approvals,
        [prevState.selectedBudget.id]: approvalData,
      },
    }));
    message.success('审批发起成功，可在操作列查看审批状态');
    this.loadWeeklyBudgets(); // 刷新列表
  };

  // 处理审批表单取消
  handleApprovalCancel = () => {
    this.setState({
      showApprovalForm: false,
      selectedBudget: null,
    });
  };

  // 查看审批状态
  handleViewApprovalStatus = async (record) => {
    // 先尝试从本地状态获取审批信息
    let approvalId;
    this.setState((prevState) => {
      approvalId = prevState.approvals[record.id]?.id;
      return null;
    }, async () => {
      if (!approvalId) {
        // 如果本地没有，尝试从服务器获取
        try {
          const response = await approvalAPI.getApprovals({
            weeklyBudgetId: record.id,
            pageSize: 1,
          });
          if (response.success && response.data.approvals.length > 0) {
            // eslint-disable-next-line require-atomic-updates
            approvalId = response.data.approvals[0].id;
          }
        } catch (error) {
          console.error('Get approvals failed:', error);
        }
      }

      if (!approvalId) {
        message.warning('该周预算暂无审批记录');
        return;
      }

      this.setState({
        showApprovalStatus: true,
        currentApprovalId: approvalId,
      });
    });
  };

  // 关闭审批状态查看
  handleCloseApprovalStatus = () => {
    this.setState({
      showApprovalStatus: false,
      currentApprovalId: null,
    });
  };

  handleViewFundingPlan = (record) => {
    this.props.onViewFundingPlan && this.props.onViewFundingPlan(record);
  };

  render() {
    const {
      weeklyBudgets,
      loading,
      pagination,
      filters,
      selectedRowKeys,
      showApprovalForm,
      showApprovalStatus,
      selectedBudget,
      currentApprovalId,
    } = this.state;
    const { onView, onEdit } = this.props;

    const columns = [
      {
        title: '预算标题',
        dataIndex: 'title',
        key: 'title',
        sorter: true,
        width: 200,
      },
      {
        title: '周期',
        key: 'weekRange',
        width: 180,
        render: (_, record) => {
          // 使用新的月内周数显示格式
          const monthWeekInfo = getMonthWeekInfoByAddition(record.weekStartDate);
          return (
            <div>
              <div style={{ fontWeight: 'bold' }}>
                {monthWeekInfo.month}月第{monthWeekInfo.weekOfMonth}周
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {formatDateRange(record.weekStartDate, record.weekEndDate)}
              </div>
            </div>
          );
        },
      },
      {
        title: '费用类型',
        dataIndex: 'serviceType',
        key: 'serviceType',
        width: 100,
        render: (serviceType) => (
          <Tag size="small">
            {getServiceTypeLabel(serviceType)}
          </Tag>
        ),
      },
      {
        title: '费用预算',
        dataIndex: 'contractAmount',
        key: 'contractAmount',
        width: 120,
        sorter: true,
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '支付金额',
        dataIndex: 'paidAmount',
        key: 'paidAmount',
        width: 120,
        sorter: true,
        render: (amount) => formatCurrency(amount),
      },
      // {
      //   title: '支付进度',
      //   key: 'paymentProgress',
      //   width: 150,
      //   render: (text, record) => {
      //     const progress = calculatePaymentProgress(record.contractAmount, record.paidAmount);
      //     const remaining = calculateRemainingAmount(record.contractAmount, record.paidAmount);

      //     return (
      //       <div>
      //         <Progress
      //           percent={progress}
      //           size="small"
      //           status={progress === 100 ? 'success' : 'active'}
      //         />
      //         <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
      //           已付: {formatCurrency(record.paidAmount || 0)} / 剩余: {formatCurrency(remaining)}
      //         </div>
      //       </div>
      //     );
      //   },
      // },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        sorter: true,
        render: (status) => {
          const config = getWeeklyBudgetStatusConfig(status);
          return <Tag color={config.color}>{config.label}</Tag>;
        },
      },
      {
        title: '供应商',
        key: 'supplier',
        width: 120,
        ellipsis: true,
        render: (text, record) => (
          <div>
            {record.supplier ? (
              <div>
                <div>{record.supplier.name}</div>
                {record.supplier.shortName && (
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {record.supplier.shortName}
                  </div>
                )}
              </div>
            ) : (
              '-'
            )}
          </div>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        sorter: true,
        render: (date) => formatDate(date),
      },
      {
        title: '操作',
        key: 'action',
        width: 180,
        fixed: 'right',
        render: (text, record) => {
          // const remainingAmount
          //   = calculateRemainingAmount(record.contractAmount, record.paidAmount);
          // const hasApproval = this.state.approvals[record.id];

          return (
            <div>
              <Button
                type="link"
                size="small"
                onClick={() => onView && onView(record)}
              >
                查看
              </Button>
              <Divider type="vertical" />
              <Button
                type="link"
                size="small"
                onClick={() => onEdit && onEdit(record)}
              >
                编辑
              </Button>
              <Divider type="vertical" />
              {/* <Button
                type="link"
                size="small"
                onClick={() => this.handleApprove(record)}
                disabled={remainingAmount <= 0}
                title={remainingAmount <= 0 ? '已全部支付，无需审批' : '发起对公付款审批'}
              >
                发起审批
              </Button>
              <Divider type="vertical" />
              <Button
                type="link"
                size="small"
                onClick={() => this.handleViewApprovalStatus(record)}
                style={{ color: hasApproval ? '#1890ff' : '#999' }}
                title="查看审批状态"
              >
                审批状态
              </Button> */}
              {/* 资金计划 */}
              <Button
                type="link"
                size="small"
                onClick={() => this.handleViewFundingPlan(record)}
              >
                资金计划
              </Button>
              <Divider type="vertical" />
              <Popconfirm
                title="确定要删除这个周预算吗？"
                onConfirm={() => this.handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                  删除
                </Button>
              </Popconfirm>
            </div>
          );
        },
      },
    ];

    const rowSelection = {
      selectedRowKeys,
      onChange: this.handleRowSelectionChange,
    };

    return (
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Select
              placeholder="服务类型"
              style={{ width: '100%' }}
              value={filters.serviceType}
              onChange={(value) => this.handleFilterChange('serviceType', value)}
              allowClear
            >
              {SERVICE_TYPES.map((type) => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="状态"
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => this.handleFilterChange('status', value)}
              allowClear
            >
              {WEEKLY_BUDGET_STATUS.map((status) => (
                <Option key={status.value} value={status.value}>
                  {status.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="年份"
              style={{ width: '100%' }}
              value={filters.year}
              onChange={(value) => this.handleFilterChange('year', value)}
              allowClear
            >
              <Option value="2024">2024年</Option>
              <Option value="2025">2025年</Option>
            </Select>
          </Col>
          <Col span={6} style={{ textAlign: 'right' }}>
            <Button
              type="danger"
              onClick={this.handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
              style={{ marginRight: 8 }}
            >
              批量删除 ({selectedRowKeys.length})
            </Button>
            <Button type="primary" onClick={this.loadWeeklyBudgets}>
              刷新
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={weeklyBudgets}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          rowSelection={rowSelection}
          onChange={this.handleTableChange}
          scroll={{ x: 1400 }}
        />

        {/* 审批表单模态框 */}
        {showApprovalForm && selectedBudget && (
          <ApprovalForm
            visible={showApprovalForm}
            weeklyBudget={selectedBudget}
            onSubmit={this.handleApprovalSubmit}
            onCancel={this.handleApprovalCancel}
          />
        )}

        {/* 审批状态查看模态框 */}
        {showApprovalStatus && (
          <ApprovalStatus
            visible={showApprovalStatus}
            approvalId={currentApprovalId}
            onCancel={this.handleCloseApprovalStatus}
          />
        )}
      </Card>
    );
  }
}

export default WeeklyBudgetTable;
