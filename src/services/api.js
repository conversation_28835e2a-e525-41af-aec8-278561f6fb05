// API服务层
import dayjs from 'dayjs';
import config from '../config.js';
import authService from './auth.js';

const { host } = config;
const API_BASE_URL = `${host}/api`;

// 通用请求方法
const request = async (url, options = {}) => {
  const { forceReloadOn401 = false, ...requestOptions } = options;

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 添加认证头
  const accessToken = authService.getAccessToken();
  if (accessToken) {
    defaultOptions.headers.Authorization = `Bearer ${accessToken}`;
  }

  const finalOptions = {
    ...defaultOptions,
    ...requestOptions,
    headers: {
      ...defaultOptions.headers,
      ...requestOptions.headers,
    },
  };

  if (finalOptions.body instanceof FormData) {
    delete finalOptions.headers['Content-Type'];
  }
  try {
    const response = await fetch(`${API_BASE_URL}${url}`, finalOptions);

    // 处理401未授权错误
    if (response.status === 401) {
      // Token可能已过期，尝试刷新
      try {
        const refreshed = await authService.refreshAccessToken();
        if (refreshed) {
          // 重新添加新的token
          const newAccessToken = authService.getAccessToken();
          if (newAccessToken) {
            finalOptions.headers.Authorization = `Bearer ${newAccessToken}`;
            // 重新发起请求
            const retryResponse = await fetch(`${API_BASE_URL}${url}`, finalOptions);
            const retryData = await retryResponse.json();

            if (!retryResponse.ok) {
              throw new Error(retryData.message || `HTTP error! status: ${retryResponse.status}`);
            }

            return retryData;
          }
        }
        throw new Error('Token刷新失败');
      } catch (refreshError) {
        // Token刷新失败，清除认证信息
        console.error('Token refresh failed:', refreshError);
        authService.clearAuth();
        // 根据选项决定是否强制刷新页面
        if (forceReloadOn401) {
          window.location.reload();
        }
        // 不再自动刷新页面，让 AppInitializer 处理认证流程
        throw new Error('登录已过期，请重新登录');
      }
    }

    // 处理导出请求
    if (response.status === 200 && finalOptions.headers.Accept === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return {
        success: true,
        data: await response.blob(),
        // 从响应头中获取文件名,需要解码,如果filename有"号,需要去除掉
        filename: decodeURIComponent(response.headers.get('Content-Disposition').split('filename=')[1].trim().replace(/["|']/g, '')),
      };
    }

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// 导出 request 函数供其他模块使用
export { request };

// 品牌相关API
export const brandAPI = {
  // 获取品牌列表
  getBrands: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.status) queryParams.append('status', params.status);
    if (params.keyword) queryParams.append('keyword', params.keyword);

    const queryString = queryParams.toString();
    return request(`/brands${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个品牌
  getBrand: (id) => {
    return request(`/brands/${id}`);
  },

  // 创建品牌
  createBrand: (brandData) => {
    return request('/brands', {
      method: 'POST',
      body: JSON.stringify(brandData),
    });
  },

  // 更新品牌
  updateBrand: (brandData) => {
    return request('/brands', {
      method: 'PUT',
      body: JSON.stringify(brandData),
    });
  },

  // 删除品牌
  deleteBrand: (id) => {
    return request(`/brands/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取品牌统计数据
  getBrandStats: (params = {}) => {
    // 开发阶段使用模拟数据
    if (process.env.NODE_ENV === 'development') {
      const { getMockBrandStats } = require('../mock/brandReportData');
      return getMockBrandStats(params);
    }

    const queryParams = new URLSearchParams();
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.brandId) queryParams.append('brandId', params.brandId);

    const queryString = queryParams.toString();
    return request(`/brands/stats${queryString ? `?${queryString}` : ''}`);
  },

  // 获取品牌执行情况
  getBrandExecution: (brandId, params = {}) => {
    // 开发阶段使用模拟数据
    if (process.env.NODE_ENV === 'development') {
      const { getMockBrandExecution } = require('../mock/brandReportData');
      return getMockBrandExecution(brandId, params);
    }

    const queryParams = new URLSearchParams();
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);

    const queryString = queryParams.toString();
    return request(`/brands/${brandId}/execution${queryString ? `?${queryString}` : ''}`);
  },

  // 获取所有品牌汇总数据
  getBrandsSummary: (params = {}) => {
    // 开发阶段使用模拟数据
    if (process.env.NODE_ENV === 'development') {
      const { getMockBrandsSummary } = require('../mock/brandReportData');
      return getMockBrandsSummary(params);
    }

    const queryParams = new URLSearchParams();
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const queryString = queryParams.toString();
    return request(`/brands/summary${queryString ? `?${queryString}` : ''}`);
  },
};

// 项目相关API
export const projectAPI = {
  // 获取项目列表
  getProjects: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.status) queryParams.append('status', params.status);
    if (params.brandId) queryParams.append('brandId', params.brandId);
    if (params.contractType) queryParams.append('contractType', params.contractType);
    if (params.contractSigningStatus) queryParams.append('contractSigningStatus', params.contractSigningStatus);
    if (params.keyword) queryParams.append('keyword', params.keyword);

    queryParams.append('orderBy', 'createdAt');
    queryParams.append('sortOrder', 'desc');
    const queryString = queryParams.toString();
    return request(`/projects${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个项目
  getProject: (id) => {
    return request(`/projects/${id}`);
  },

  // 创建项目
  createProject: (projectData) => {
    return request('/projects', {
      method: 'POST',
      body: JSON.stringify(projectData),
    });
  },

  // 更新项目
  updateProject: (projectData) => {
    console.log('[ projectData ] >', projectData);
    return request('/projects', {
      method: 'PUT',
      body: JSON.stringify(projectData),
    });
  },

  // 删除项目
  deleteProject: (id) => {
    return request(`/projects/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取项目统计
  getProjectStats: () => {
    return request('/projects/stats');
  },

  // 获取项目变更记录
  getProjectChangeLogs: (projectId, params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.changeType) queryParams.append('changeType', params.changeType);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.operator) queryParams.append('operator', params.operator);
    if (params.searchText) queryParams.append('searchText', params.searchText);

    const queryString = queryParams.toString();
    return request(`/projects/${projectId}/change-logs${queryString ? `?${queryString}` : ''}`);
  },

  exportWeeklyBudgets: ({ year, month }) => {
    return request(`/monthly-budget/export?year=${year}&month=${month}`, {
      method: 'GET',
      headers: {
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    });
  },
};

// 文件上传API
export const uploadAPI = {
  // 上传文件
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request('/app/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  // 上传文件
  uploadProjectFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request('/projects/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export const approvalUploadAPI = {
  // 上传文件
  uploadFile: (type, file) => {
    if (!type) {
      throw new Error('文件类型不能为空');
    }
    const formData = new FormData();
    formData.append('file', file);

    return request(`/approvals/upload/${type}`, {
      method: 'POST',
      body: formData,
    });
  },
};

// 数据转换工具
export const dataTransform = {
  // 将前端项目数据转换为API格式
  projectToAPI: (formData) => {
    console.log('[ formData ] >', formData);
    return {
      documentType: formData.documentType,
      brandId: formData.brand,
      projectName: formData.projectName,
      period: {
        startDate: dayjs(formData.executionPeriod[0]).format('YYYY-MM-DD'),
        endDate: dayjs(formData.executionPeriod[1]).format('YYYY-MM-DD'),
      },
      budget: {
        planningBudget: formData.planningBudget,
        influencerBudget: formData.talentBudget,
        adBudget: formData.adBudget,
        otherBudget: formData.otherBudget,
      },
      cost: {
        influencerCost: formData.talentCost,
        adCost: formData.adCost,
        otherCost: formData.otherCost,
        estimatedInfluencerRebate: formData.estimatedTalentRebate,
      },
      executorPM: formData.executivePM,
      contentMediaIds: formData.contentMedia,
      contractType: formData.contractType.toLowerCase(),
      contractSigningStatus: formData.contractSigningStatus.toLocaleLowerCase(),
      settlementRules: formData.settlementRules,
      kpi: formData.kpi,
      expectedPaymentMonth: formData.expectedPaymentMonth
        ? dayjs(formData.expectedPaymentMonth).format('YYYY-MM')
        : null,
      paymentTermDays: formData.paymentTermDays || null,
      attachmentIds: formData.attachments.map((item) => (
        item.response ? item.response.id : item.id
      )),
    };
  },

  // 将API项目数据转换为前端格式
  projectFromAPI: (apiData) => {
    const projectProfit = apiData.budget.planningBudget
      - apiData.cost.influencerCost
      - apiData.cost.adCost
      - apiData.cost.otherCost
      + apiData.cost.estimatedInfluencerRebate;
    const grossMargin = projectProfit / apiData.budget.planningBudget * 100;
    return {
      id: apiData.id,
      documentType: apiData.documentType,
      brand: apiData.brand.id,
      brandName: apiData.brand.name,
      projectName: apiData.projectName,
      executionPeriod: [dayjs(apiData.period.startDate).format('YYYY-MM-DD'), dayjs(apiData.period.endDate).format('YYYY-MM-DD')],
      planningBudget: apiData.budget.planningBudget,
      talentBudget: apiData.budget.influencerBudget,
      adBudget: apiData.budget.adBudget,
      otherBudget: apiData.budget.otherBudget,
      talentCost: apiData.cost.influencerCost,
      adCost: apiData.cost.adCost,
      otherCost: apiData.cost.otherCost,
      // 将API字段映射到新的前端字段名
      talentRebateIncome: apiData.cost.estimatedInfluencerRebate,
      // 保留旧字段名以兼容现有代码
      estimatedTalentRebate: apiData.cost.estimatedInfluencerRebate,
      executivePM: apiData.executorPMInfo || '-',
      contentMedia: Array.isArray(apiData.contentMediaIds)
        ? apiData.contentMediaIds.map((media) => media?.name || media?.userid || media).join(', ')
        : apiData.contentMediaIds || '-',
      contractType: apiData.contractType,
      contractSigningStatus: apiData.contractSigningStatus,
      settlementRules: apiData.settlementRules,
      projectProfit,
      grossMargin,
      kpi: apiData.kpi,
      expectedPaymentMonth: apiData.expectedPaymentMonth || null,
      paymentTermDays: apiData.paymentTermDays || null,
      status: apiData.status,
      createTime: dayjs(apiData.createdAt).format('YYYY-MM-DD HH:mm'),
      updateTime: dayjs(apiData.updatedAt).format('YYYY-MM-DD HH:mm'),
      attachments: apiData.attachments || [],
    };
  },

  // 将前端品牌数据转换为API格式
  brandToAPI: (formData) => {
    return {
      name: formData.name,
      description: formData.description || '',
      logo: formData.logo || '',
      status: formData.status,
    };
  },

  // 将API品牌数据转换为前端格式
  brandFromAPI: (apiData) => {
    return {
      id: apiData.id,
      name: apiData.name,
      code: apiData.id, // 使用ID作为code
      description: apiData.description,
      logo: apiData.logo,
      status: apiData.status,
      createTime: apiData.createdAt,
      updateTime: apiData.updatedAt,
    };
  },
};

// 收入管理API
export const revenueAPI = {
  // 创建项目收入
  createRevenue: (projectId, revenueData) =>
    request(`/projects/${projectId}/revenues`, {
      method: 'POST',
      body: JSON.stringify(revenueData),
    }),

  // 获取收入列表
  getRevenues: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return request(`/revenues${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个收入详情
  getRevenue: (id) =>
    request(`/revenues/${id}`),

  // 更新收入
  updateRevenue: (id, revenueData) =>
    request(`/revenues/${id}`, {
      method: 'PUT',
      body: JSON.stringify(revenueData),
    }),

  // 删除收入
  deleteRevenue: (id) =>
    request(`/revenues/${id}`, {
      method: 'DELETE',
    }),

  // 获取收入统计
  getRevenueStats: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return request(`/revenues/stats${queryString ? `?${queryString}` : ''}`);
  },

  // 批量更新收入状态
  batchUpdateStatus: (ids, status) =>
    request('/revenues/batch-status', {
      method: 'PUT',
      body: JSON.stringify({ ids, status }),
    }),

  // 确认收入
  confirmRevenue: (id, confirmData) =>
    request(`/revenues/${id}/confirm`, {
      method: 'PUT',
      body: JSON.stringify(confirmData),
    }),

  // 批量确认收入
  batchConfirmRevenues: (revenues) =>
    request('/revenues/batch-confirm', {
      method: 'PUT',
      body: JSON.stringify({ revenues }),
    }),
};

// 供应商管理API
export const supplierAPI = {
  // 获取供应商列表
  getSuppliers: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.status) queryParams.append('status', params.status);
    if (params.serviceType) queryParams.append('serviceType', params.serviceType);
    if (params.keyword) queryParams.append('keyword', params.keyword);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    return request(`/suppliers${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个供应商
  getSupplier: (id) => {
    return request(`/suppliers/${id}`);
  },

  // 创建供应商
  createSupplier: (supplierData) => {
    return request('/suppliers', {
      method: 'POST',
      body: JSON.stringify(supplierData),
    });
  },

  // 更新供应商
  updateSupplier: (id, supplierData) => {
    return request(`/suppliers/${id}?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(supplierData),
    });
  },

  // 删除供应商
  deleteSupplier: (id) => {
    return request(`/suppliers/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取供应商统计
  getSupplierStats: () => {
    return request('/suppliers/stats');
  },

  // 批量删除供应商
  batchDeleteSuppliers: (ids) => {
    return request('/suppliers/batch-delete', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    });
  },

  // 批量更新供应商状态
  batchUpdateSupplierStatus: (ids, status) => {
    return request('/suppliers/batch-status', {
      method: 'PUT',
      body: JSON.stringify({ ids, status }),
    });
  },
};

// 周预算管理API
export const weeklyBudgetAPI = {
  // 获取周预算列表
  getWeeklyBudgets: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.projectId) queryParams.append('projectId', params.projectId);
    if (params.supplierId) queryParams.append('supplierId', params.supplierId);
    if (params.serviceType) queryParams.append('serviceType', params.serviceType);
    if (params.status) queryParams.append('status', params.status);
    if (params.year) queryParams.append('year', params.year);
    if (params.weekNumber) queryParams.append('weekNumber', params.weekNumber);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    return request(`/weekly-budgets${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个周预算
  getWeeklyBudget: (id) => {
    return request(`/weekly-budgets/${id}`);
  },

  // 创建项目周预算
  createWeeklyBudget: (projectId, budgetData) => {
    return request(`/projects/${projectId}/weekly-budgets`, {
      method: 'POST',
      body: JSON.stringify(budgetData),
    });
  },

  // 批量创建项目周预算
  batchCreateWeeklyBudgets: (projectId, batchData) => {
    return request(`/projects/${projectId}/weekly-budgets/batch`, {
      method: 'POST',
      body: JSON.stringify(batchData),
    });
  },

  // 更新周预算
  updateWeeklyBudget: (id, budgetData) => {
    return request(`/weekly-budgets/${id}`, {
      method: 'PUT',
      body: JSON.stringify(budgetData),
    });
  },

  // 删除周预算
  deleteWeeklyBudget: (id) => {
    return request(`/weekly-budgets/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取周预算统计
  getWeeklyBudgetStats: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.projectId) queryParams.append('projectId', params.projectId);
    if (params.year) queryParams.append('year', params.year);

    const queryString = queryParams.toString();
    return request(`/weekly-budgets/stats${queryString ? `?${queryString}` : ''}`);
  },

  // 批量删除周预算
  batchDeleteWeeklyBudgets: (ids) => {
    return request('/weekly-budgets/batch-delete', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    });
  },

  // 批量更新周预算状态
  batchUpdateWeeklyBudgetStatus: (ids, status) => {
    return request('/weekly-budgets/batch-status', {
      method: 'PUT',
      body: JSON.stringify({ ids, status }),
    });
  },
};

// 审批管理API
export const approvalAPI = {
  // 发起对公付款审批
  createApproval: (approvalData) => {
    return request('/weekly-budgets/approval', {
      method: 'POST',
      body: JSON.stringify(approvalData),
    });
  },

  // 获取单个审批实例
  getApproval: (id) => {
    return request(`/approvals/${id}`);
  },

  // 同步审批状态
  syncApprovalStatus: (processInstanceId) => {
    return request(`/approvals/sync/${processInstanceId}`, {
      method: 'GET',
    });
  },

  // 获取审批列表
  getApprovals: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.status) queryParams.append('status', params.status);
    if (params.weeklyBudgetId) queryParams.append('weeklyBudgetId', params.weeklyBudgetId);
    if (params.projectId) queryParams.append('projectId', params.projectId);

    const queryString = queryParams.toString();
    return request(`/approvals${queryString ? `?${queryString}` : ''}`);
  },
};

// 部门用户管理API
export const departmentAPI = {
  // 获取部门用户列表
  getDepartmentUsers: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.deptId) queryParams.append('deptId', params.deptId);
    if (params.cursor) queryParams.append('cursor', params.cursor);
    if (params.size) queryParams.append('size', params.size);

    const queryString = queryParams.toString();
    return request(`/app/department/users${queryString ? `?${queryString}` : ''}`);
  },

  getDepartments: () => {
    return request('/departments');
  },
};


// 用户管理API
export const userApi = {
  // 批量根据用户ID获取用户名
  // post /users/names
  fetchUserNames: (userIds) => {
    return request('/users/names', {
      method: 'POST',
      body: JSON.stringify(userIds),
    });
  },

  // get /users/:userid/name
  fetchUserName: (userId) => {
    return request(`/users/${userId}/name`);
  },

  // 获取用户列表
  getUsers: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.status) queryParams.append('status', params.status);
    if (params.departmentIds) queryParams.append('departmentIds', params.departmentIds);
    if (params.keyword) queryParams.append('keyword', params.keyword);
    if (params.roleIds) queryParams.append('roleIds', params.roleIds);

    const queryString = queryParams.toString();
    return request(`/users${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个用户详情
  getUser: (id) => {
    return request(`/users/${id}`);
  },

  // 更新用户信息
  updateUser: (id, userData) => {
    return request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  // 同步钉钉用户
  syncDingTalkUsers: (params = {}) => {
    return request('/users/sync-all', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  },

  // 批量更新用户角色
  updateUserRoles: (userId, roleIds) => {
    return request(`/users/${userId}/roles`, {
      method: 'POST',
      body: JSON.stringify({ roleIds }),
    });
  },

  // 启用/禁用用户
  updateUserStatus: (userId, status) => {
    return request(`/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },


  // 获取当前用户
  getCurrentUser: () => {
    return request('/me');
  },

  // 获取当前用户
  getCurrentUserInfo: () => {
    return request('/auth/me');
  },
  getCurrentUserPermissions: () => {
    return request('/me/permissions');
  },
};

// 钉钉API已移动到独立的 dingApi.js 文件中

// 角色管理API
export const roleApi = {
  // 获取角色列表
  getRoles: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.pageSize) queryParams.append('pageSize', params.pageSize);
    if (params.isActive) queryParams.append('isActive', params.isActive);
    if (params.keyword) queryParams.append('search', params.keyword);

    const queryString = queryParams.toString();
    return request(`/roles${queryString ? `?${queryString}` : ''}`);
  },

  // 获取单个角色详情
  getRole: (id) => {
    return request(`/roles/${id}`);
  },

  // 创建角色
  createRole: (roleData) => {
    return request('/roles', {
      method: 'POST',
      body: JSON.stringify(roleData),
    });
  },

  // 更新角色
  updateRole: (id, roleData) => {
    return request(`/roles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(roleData),
    });
  },

  // 删除角色
  deleteRole: (id) => {
    return request(`/roles/${id}`, {
      method: 'DELETE',
      body: JSON.stringify({}),
    });
  },

  // 获取角色权限
  getRolePermissions: (roleId) => {
    return request(`/roles/${roleId}/permissions`);
  },

  // 更新角色权限
  updateRolePermissions: (roleId, permissionIds) => {
    return request(`/roles/${roleId}/permissions`, {
      method: 'POST',
      body: JSON.stringify({ permissionIds }),
    });
  },

  // 获取角色关联的部门
  getRoleDepartments: (roleId) => {
    return request(`/roles/${roleId}/departments`);
  },

  // 更新角色关联的部门
  updateRoleDepartments: (roleId, departmentIds) => {
    return request(`/roles/${roleId}/departments`, {
      method: 'POST',
      body: JSON.stringify({ deptIds: departmentIds }),
    });
  },

  getRolesByUserId: (userId) => {
    return request(`/users/${userId}/roles`);
  },
};

// 权限管理API
export const permissionApi = {
  // 获取权限列表
  getPermissions: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.module) queryParams.append('module', params.module);
    if (params.search) queryParams.append('search', params.search);

    const queryString = queryParams.toString();
    return request(`/permissions${queryString ? `?${queryString}` : ''}`);
  },

  // 获取权限分类
  getPermissionCategories: () => {
    return request('/permissions/categories');
  },

  // 创建权限
  createPermission: (permissionData) => {
    return request('/permissions', {
      method: 'POST',
      body: JSON.stringify(permissionData),
    });
  },

  // 更新权限
  updatePermission: (id, permissionData) => {
    return request(`/permissions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(permissionData),
    });
  },

  // 删除权限
  deletePermission: (id) => {
    return request(`/permissions/${id}`, {
      method: 'DELETE',
    });
  },
};

export const statisticsAPI = {
  getStatistics: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const queryString = queryParams.toString();
    return request(`/financial/statistics${queryString ? `?${queryString}` : ''}`);
  },
};
